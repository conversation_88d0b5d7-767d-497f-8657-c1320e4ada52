<?php

namespace Drupal\bribe\Normalizer;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\rest_entity_recursive\Normalizer\ContentEntityNormalizer;

class ReferenceContenTypeNormalizer extends ContentEntityNormalizer
{
    /**
     * The interface or class that this Normalizer supports.
     *
     * @var string
     */
    protected $supportedInterfaceOrClass = 'Drupal\paragraphs\ParagraphInterface';

    /**
     * Array of supported paragraph types.
     *
     * @var array
     */
    protected $supportedParagraphTypes = ['content_type'];

    /**
     * @var \Drupal\Core\Entity\EntityTypeManagerInterface
     */
    private EntityTypeManagerInterface $em;

    public function __construct(EntityTypeManagerInterface $entityTypeManager)
    {
        $this->em = $entityTypeManager;
    }

    /**
     * @inheritDoc
     */
    public function supportsNormalization(
        $data,
        ?string $format = NULL,
        array $context = []
    ): bool {
        if (parent::supportsNormalization($data, $format)) {
            if (empty($this->supportedParagraphTypes)) {
                return TRUE;
            }
            if (in_array($data->getType(), $this->supportedParagraphTypes)) {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * @inheritDoc
     */

    public function normalize($entity, $format = NULL, array $context = []): array|\ArrayObject|bool|float|int|string|null
    {
        $normalized = parent::normalize(
            $entity,
            $format,
            $context
        );

        $request = \Drupal::request();
        $langcode = $entity->language()->getId();

        $limit = $request->query->get('limit', 0);
        $page = $request->query->get('page', 0);
        $offset = max(($page - 1) * $limit, 0);

        $getContentType = $entity->get('field_content_type')->target_id;
        $categories = $entity->get('field_category_product')->getValue();
        $getlimit = !empty($entity->get('field_show_limit')->getValue()) ? $entity->get('field_show_limit')->getValue()[0]['value'] : '';

        $sites = $entity->get('field_site')->getValue();
        $cats = [];
        foreach ($categories as $cat) {
            $cats[] = $cat['value'];
        }
        $sitelist = [];
        foreach ($sites as $site) {
            $sitelist[] = $site['value'];
        }

        $node_storage = \Drupal::entityTypeManager()->getStorage('node');
        $query = $node_storage->getQuery();
        $query->accessCheck(false);
        $query->condition('type', $getContentType);

        $pager = $node_storage->getQuery();
        $pager->accessCheck(false);
        $pager->condition('type', $getContentType);

        if ($getContentType == 'product') {
            if (!empty($cats)) {
                $query->condition('field_category', $cats, 'IN');
                $pager->condition('field_category', $cats, 'IN');
            }
            if (!empty($sitelist)) {
                $query->condition('field_category', $cats, 'IN');
                $pager->condition('field_category', $cats, 'IN');
            }
        }

        $query->sort('changed', 'DESC');
        
        if (!empty($getlimit)) {
            if($request->query->has('page')){
                $query->range($offset, $limit);
            } else {
                $query->range(0, $getlimit);
            }
        }

        $nids = $query->execute();

        $nodes = $node_storage->loadMultiple($nids);

        $total = $pager->count()->execute();

        $data = [];
        foreach ($nodes as $node) {
            // Get the details of each node
            $fields_data = [];

            // Get all field definitions for the node type
            $field_definitions = $node->getFieldDefinitions();

            // Loop through each field definition and get its value
            foreach ($field_definitions as $field_name => $field_definition) {
                print("Test: " . $field_name . " " . $field_definition->getType() . " = " . $langcode);

                if (strpos($field_definition->getType(), 'entity_reference') !== false) {
                    // Get the referenced entities
                    $referenced_entities = $node->get($field_name)->referencedEntities();
                    $fields_data[$field_name] = $this->serializer->normalize($referenced_entities, 'json_recursive');

                } elseif ($field_definition->getType() == 'created' || $field_definition->getType() == 'changed' ) {
                    $datetime_value = $node->get($field_name)->getValue();
                    if (!empty($datetime_value)) {
                        $fields_data[$field_name] = [
                                array(
                                    'value' => date( 'Y-m-d\TH:i:sP', $datetime_value[0]['value']),
                                    'format' => 'Y-m-d\TH:i:sP'
                                )
                            ];
                    } else {
                        $fields_data[$field_name] = $node->get($field_name)->getValue();
                    }

                } else {
                    // Get the field value
                    $fields_data[$field_name] = $node->get($field_name)->getValue();
                }
            }

            $data[] = $fields_data;
        }
        $normalized['field_content_type'] = $data ?: [];

        $normalized['pager'] = array(
            'total' => $total,
            'limit' => (int) $limit,
            'page' => (int) $page,
            'total_page' => $limit > 0 ? ceil($total / $limit) : 0
        );

        return $normalized;
    }
}
